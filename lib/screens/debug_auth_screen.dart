import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/aws_auth_service.dart';
import '../services/api_service.dart';
import '../services/websocket_service.dart';
import '../services/notification_service.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../pages/websocket_test_page.dart';
import 'cache_debug_screen.dart';

/// Debug screen for testing authentication and token refresh
class DebugAuthScreen extends StatefulWidget {
  const DebugAuthScreen({super.key});

  @override
  State<DebugAuthScreen> createState() => _DebugAuthScreenState();
}

class _DebugAuthScreenState extends State<DebugAuthScreen> {
  Map<String, dynamic>? _debugInfo;
  String? _lastTestResult;
  bool _isLoading = false;
  String _selectedApiCall = 'notifications';

  @override
  void initState() {
    super.initState();
    _refreshDebugInfo();
  }

  void _refreshDebugInfo() {
    setState(() {
      _debugInfo = AwsAuthService.instance.getDebugInfo();
    });
  }

  Future<void> _testApiCall() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      final endpoint = _getApiEndpoint(_selectedApiCall);
      AppLogger.debug('Testing API call to $endpoint...');

      dynamic response;

      // Handle different HTTP methods based on the endpoint
      if (_selectedApiCall == 'test-notification') {
        // POST request for creating test notification
        response = await ApiService.instance.makeAuthenticatedRequest(
          method: 'POST',
          path: endpoint,
          body: {
            'type': 'follow', // Default to follow notification
            'title': 'Test Notification',
            'message':
                'This is a test notification created from the debug panel',
          },
        );
      } else {
        // GET request for other endpoints
        response = await ApiService.instance.makeAuthenticatedRequest(
          method: 'GET',
          path: endpoint,
        );
      }

      final data = ApiService.instance.parseResponse(response);

      // Format the response based on the endpoint
      String resultMessage = _formatApiResponse(_selectedApiCall, data);

      setState(() {
        _lastTestResult = 'SUCCESS ($_selectedApiCall):\n$resultMessage';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = 'ERROR ($_selectedApiCall): $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _refreshDebugInfo();
    }
  }

  String _getApiEndpoint(String apiCall) {
    switch (apiCall) {
      case 'notifications':
        return '/notifications?limit=10';
      case 'health':
        return '/health';
      case 'channels':
        return '/channels?limit=5';
      case 'posts':
        return '/posts?limit=5';
      case 'users':
        return '/users/me'; // Get current user info
      case 'test-notification':
        return '/notifications/test';
      default:
        return '/health';
    }
  }

  String _formatApiResponse(String apiCall, dynamic data) {
    try {
      switch (apiCall) {
        case 'notifications':
          if (data is Map && data.containsKey('notifications')) {
            final notifications = data['notifications'] as List?;
            return 'Got ${notifications?.length ?? 0} notifications';
          }
          return 'Response: $data';
        case 'test-notification':
          if (data is Map && data.containsKey('message')) {
            final notification = data['notification'] as Map?;
            return 'Test notification created!\n'
                'Type: ${notification?['type'] ?? 'N/A'}\n'
                'Title: ${notification?['title'] ?? 'N/A'}\n'
                'Body: ${notification?['body'] ?? 'N/A'}\n'
                'Status: ${data['message']}';
          }
          return 'Response: $data';
        case 'health':
          if (data is Map && data.containsKey('status')) {
            return 'Status: ${data['status']}, Message: ${data['message'] ?? 'OK'}';
          }
          return 'Response: $data';
        case 'channels':
          if (data is Map && data.containsKey('channels')) {
            final channels = data['channels'] as List?;
            return 'Got ${channels?.length ?? 0} channels';
          }
          return 'Response: $data';
        case 'posts':
          if (data is Map && data.containsKey('posts')) {
            final posts = data['posts'] as List?;
            return 'Got ${posts?.length ?? 0} posts';
          }
          return 'Response: $data';
        case 'users':
          if (data is Map && data.containsKey('user')) {
            final user = data['user'] as Map?;
            return 'User: ${user?['username'] ?? 'N/A'} (${user?['email'] ?? 'N/A'})';
          }
          return 'Response: $data';
        default:
          return 'Response: $data';
      }
    } catch (e) {
      return 'Response formatting error: $e\nRaw data: $data';
    }
  }

  Future<void> _forceTokenRefresh() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      final success = await AwsAuthService.instance.forceTokenRefresh();
      setState(() {
        _lastTestResult =
            success ? 'Token refresh SUCCESS' : 'Token refresh FAILED';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = 'Token refresh ERROR: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _refreshDebugInfo();
    }
  }

  Future<void> _testMultipleApiCalls() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      AppLogger.debug('Testing multiple API calls in sequence...');

      // Make multiple API calls to test token refresh behavior
      final results = <String>[];

      for (int i = 1; i <= 3; i++) {
        try {
          final response = await ApiService.instance.makeAuthenticatedRequest(
            method: 'GET',
            path: '/posts?limit=1',
          );
          final data = ApiService.instance.parseResponse(response);
          results.add('Call $i: SUCCESS (${data['posts']?.length ?? 0} posts)');
        } catch (e) {
          results.add('Call $i: ERROR - $e');
        }

        // Small delay between calls
        await Future.delayed(const Duration(milliseconds: 500));
      }

      setState(() {
        _lastTestResult = 'Multiple API calls:\n${results.join('\n')}';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = 'Multiple API calls ERROR: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _refreshDebugInfo();
    }
  }

  Future<void> _testConnectivity() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      final result = await ApiService.instance.testConnectivity();
      setState(() {
        _lastTestResult =
            'Connectivity: ${result['status']} - ${result['message']}';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = 'Connectivity ERROR: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testWebSocket() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      final wsService = WebSocketService.instance;

      // Get current status
      final isConnected = wsService.isConnected;
      final currentState = wsService.connectionState.name;

      if (!isConnected) {
        // Try to connect
        await wsService.connect();

        // Wait a moment for connection to establish
        await Future.delayed(const Duration(seconds: 2));

        final newState = wsService.connectionState.name;
        final newConnected = wsService.isConnected;

        setState(() {
          _lastTestResult =
              'WebSocket Test:\n'
              'Initial State: $currentState\n'
              'After Connect: $newState\n'
              'Connected: $newConnected\n'
              'Status: ${wsService.statusMessage}\n'
              'Quality: ${wsService.connectionQuality}%';
        });
      } else {
        // Already connected, show status
        setState(() {
          _lastTestResult =
              'WebSocket Status:\n'
              'State: $currentState\n'
              'Connected: $isConnected\n'
              'Status: ${wsService.statusMessage}\n'
              'Quality: ${wsService.connectionQuality}%\n'
              'Healthy: ${wsService.isHealthy}';
        });
      }
    } catch (e) {
      setState(() {
        _lastTestResult = 'WebSocket ERROR: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestNotification() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      AppLogger.debug('Creating test notification...');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/notifications/test',
        body: {
          'type': 'follow',
          'title': 'Debug Test Notification',
          'message':
              'This is a test notification created from the Auth Debug screen',
        },
      );

      final data = ApiService.instance.parseResponse(response);

      if (data.containsKey('message')) {
        final notification = data['notification'] as Map?;
        setState(() {
          _lastTestResult =
              'Test Notification Created Successfully!\n\n'
              'Type: ${notification?['type'] ?? 'N/A'}\n'
              'Title: ${notification?['title'] ?? 'N/A'}\n'
              'Body: ${notification?['body'] ?? 'N/A'}\n\n'
              'Status: ${data['message']}\n\n'
              'Check your notifications list to see the new notification!';
        });
      } else {
        setState(() {
          _lastTestResult =
              'Test notification created, but unexpected response format:\n$data';
        });
      }
    } catch (e) {
      setState(() {
        _lastTestResult = 'Failed to create test notification:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _refreshDebugInfo();
    }
  }

  Future<void> _registerDeviceToken() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = null;
    });

    try {
      AppLogger.debug('Registering device token...');

      await NotificationService.instance.registerDeviceToken();

      setState(() {
        _lastTestResult =
            'Device Token Registration Successful!\n\n'
            'Device ID: ${NotificationService.instance.deviceId}\n'
            'FCM Token: ${NotificationService.instance.fcmToken}\n\n'
            'The device is now registered to receive push notifications.\n'
            'Try creating a test notification to see if it gets delivered!';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = 'Failed to register device token:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _refreshDebugInfo();
    }
  }

  void _copyDebugInfo() {
    if (_debugInfo != null) {
      final info = _debugInfo!.entries
          .map((e) => '${e.key}: ${e.value}')
          .join('\n');
      Clipboard.setData(ClipboardData(text: info));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Debug info copied to clipboard')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text('Auth Debug'),
        backgroundColor: AppColors.gfDarkBlue,
        foregroundColor: AppColors.gfOffWhite,
        actions: [
          IconButton(
            onPressed: _refreshDebugInfo,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Debug Info Card
            Card(
              color: AppColors.gfDarkBlue,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Authentication Status',
                          style: TextStyle(
                            color: AppColors.gfOffWhite,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: _copyDebugInfo,
                          icon: const Icon(
                            Icons.copy,
                            color: AppColors.gfOffWhite,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (_debugInfo != null) ...[
                      ..._debugInfo!.entries.map(
                        (entry) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 140,
                                child: Text(
                                  '${entry.key}:',
                                  style: const TextStyle(
                                    color: AppColors.gfGreen,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  '${entry.value}',
                                  style: const TextStyle(
                                    color: AppColors.gfOffWhite,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Results Card
            if (_lastTestResult != null) ...[
              Card(
                color:
                    _lastTestResult!.contains('ERROR') ||
                            _lastTestResult!.contains('FAILED')
                        ? Colors.red.shade900
                        : Colors.green.shade900,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Last Test Result',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastTestResult!,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // API Call Selection
            Card(
              color: AppColors.gfDarkBlue,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'API Call Selection',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Text(
                          'Endpoint: ',
                          style: TextStyle(color: AppColors.gfOffWhite),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: DropdownButton<String>(
                            value: _selectedApiCall,
                            isExpanded: true,
                            dropdownColor: AppColors.gfDarkBlue,
                            style: const TextStyle(color: AppColors.gfOffWhite),
                            onChanged:
                                _isLoading
                                    ? null
                                    : (String? newValue) {
                                      if (newValue != null) {
                                        setState(() {
                                          _selectedApiCall = newValue;
                                        });
                                      }
                                    },
                            items: const [
                              DropdownMenuItem(
                                value: 'notifications',
                                child: Text(
                                  'Get Notifications (/notifications)',
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'test-notification',
                                child: Text(
                                  'Create Test Notification (POST /notifications/test)',
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'health',
                                child: Text('Health Check (/health)'),
                              ),
                              DropdownMenuItem(
                                value: 'channels',
                                child: Text('Channels (/channels)'),
                              ),
                              DropdownMenuItem(
                                value: 'posts',
                                child: Text('Posts (/posts)'),
                              ),
                              DropdownMenuItem(
                                value: 'users',
                                child: Text('Current User (/users/me)'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testApiCall,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.gfGreen,
                          foregroundColor: AppColors.gfDarkBlue,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child:
                            _isLoading
                                ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : Text('Execute $_selectedApiCall API'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Buttons
            const Text(
              'Test Actions',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testConnectivity,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.gfGreen,
                  foregroundColor: AppColors.gfDarkBlue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Test API Connectivity'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _forceTokenRefresh,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Force Token Refresh'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testMultipleApiCalls,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Test Multiple API Calls'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testWebSocket,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Test WebSocket Connection'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _registerDeviceToken,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Register Device Token'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _createTestNotification,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Create Test Notification'),
              ),
            ),

            const SizedBox(height: 16),

            // Navigation to WebSocket Debug Panel
            const Text(
              'Debug Panels',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const WebSocketTestPage(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Open WebSocket Debug Panel'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CacheDebugScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Open Cache Debug Panel'),
              ),
            ),

            const SizedBox(height: 24),

            // Instructions
            Card(
              color: AppColors.gfDarkBlue.withValues(alpha: 0.5),
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debug Panel Instructions:',
                      style: TextStyle(
                        color: AppColors.gfGreen,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'API Testing:\n'
                      '• Select an endpoint from the dropdown\n'
                      '• Click "Execute [endpoint] API" to make the call\n'
                      '• View formatted response in the result card\n\n'
                      'Test Notifications (Dev Only):\n'
                      '• Select "Create Test Notification" from dropdown OR\n'
                      '• Click "Create Test Notification" button\n'
                      '• Creates a test follow notification for your account\n'
                      '• Check your notifications list to see the result\n\n'
                      'WebSocket Testing:\n'
                      '• Click "Test WebSocket Connection" to check connectivity\n'
                      '• View connection status, quality, and health info\n\n'
                      'Cache Management:\n'
                      '• Open "Cache Debug Panel" to view feed cache statistics\n'
                      '• Clear specific feed caches or all cached data\n'
                      '• Export cache data for analysis\n\n'
                      'Token Management:\n'
                      '• Use "Force Token Refresh" to manually refresh JWT\n'
                      '• "Test Multiple API Calls" tests token refresh behavior\n'
                      '• Check logs for detailed token refresh activity',
                      style: TextStyle(color: AppColors.gfOffWhite),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
