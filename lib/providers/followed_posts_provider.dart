import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../models/feed_state_model.dart';
import '../services/aws_posts_service.dart';
import '../services/aws_auth_service.dart';
import '../services/feed_cache_manager.dart';
import '../utils/app_logger.dart';

enum FollowedPostsStatus { initial, loading, loaded, refreshing, error }

class FollowedPostsProvider with ChangeNotifier {
  List<PostModel> _posts = [];
  FollowedPostsStatus _status = FollowedPostsStatus.initial;
  String? _error;
  int _currentOffset = 0;
  bool _hasMore = true;
  int _currentPostIndex = 0;
  final int _pageSize = 20;
  static const String _feedType = 'followed';

  // Track cache vs network loading for dev debugging
  final bool _lastLoadWasFromCache = false;
  bool _currentlyDisplayingFromCache =
      false; // Mutable field for display status

  // Cache manager instance
  final FeedCacheManager _cacheManager = FeedCacheManager.instance;

  // Getters
  List<PostModel> get posts => _posts;
  FollowedPostsStatus get status => _status;
  String? get error => _error;
  bool get hasMore => _hasMore;
  int get currentPostIndex => _currentPostIndex;
  bool get lastLoadWasFromCache =>
      _currentlyDisplayingFromCache; // Use display status instead

  /// Load followed posts
  Future<void> loadPosts() async {
    if (_status == FollowedPostsStatus.loading) return;

    // Check if user is authenticated before loading posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'loadFollowedPosts: User not authenticated, skipping load',
      );
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    // Try to load from cache first
    final cachedFeed = _cacheManager.getCachedFeed(_feedType);
    if (cachedFeed != null && !_cacheManager.shouldRefreshFeed(_feedType)) {
      AppLogger.provider(
        'loadFollowedPosts: Loading from cache with ${cachedFeed.posts.length} posts',
      );
      _posts = cachedFeed.posts;
      _currentPostIndex = cachedFeed.currentPostIndex;
      _currentOffset = cachedFeed.currentOffset;
      _hasMore = cachedFeed.hasMore;
      _currentlyDisplayingFromCache = true; // Mark as loaded from cache
      _setStatus(FollowedPostsStatus.loaded);
      return;
    }

    AppLogger.provider('loadFollowedPosts: Loading from network');
    developer.log(
      'FollowedPostsProvider: Starting to load followed posts from network',
    );
    _setStatus(FollowedPostsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;
    _currentlyDisplayingFromCache =
        false; // Reset cache status for network load

    try {
      AppLogger.provider(
        'loadFollowedPosts: About to call AwsPostsService.getFollowedPosts()',
      );
      developer.log(
        'FollowedPostsProvider: Calling AwsPostsService.getFollowedPosts()',
      );
      final posts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      AppLogger.provider(
        'loadFollowedPosts: Received ${posts.length} posts from service',
      );
      developer.log(
        'FollowedPostsProvider: Loaded ${posts.length} followed posts',
      );

      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;

      // Only reset to first post if this is a completely fresh load (no existing posts)
      if (_currentPostIndex >= posts.length) {
        _currentPostIndex = 0;
        AppLogger.provider(
          'FollowedPostsProvider: Reset currentPostIndex to 0 (was out of bounds)',
        );
      } else {
        AppLogger.provider(
          'FollowedPostsProvider: Keeping currentPostIndex at $_currentPostIndex',
        );
      }

      // Cache the loaded posts
      final feedState = FeedState(
        feedType: _feedType,
        posts: _posts,
        currentPostIndex: _currentPostIndex,
        lastRefreshTime: DateTime.now(),
        lastAccessTime: DateTime.now(),
        currentOffset: _currentOffset,
        hasMore: _hasMore,
      );
      _cacheManager.cacheFeed(feedState);

      _setStatus(FollowedPostsStatus.loaded);

      AppLogger.provider(
        'loadFollowedPosts: Successfully loaded ${_posts.length} posts',
      );
    } catch (e, stackTrace) {
      developer.log(
        'FollowedPostsProvider: Error loading followed posts',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'FollowedPostsProvider ERROR',
        error: e,
        stackTrace: stackTrace,
      );

      // Check if it's an authentication error
      if (e.toString().contains('401') ||
          e.toString().contains('403') ||
          e.toString().contains('User not authenticated')) {
        _setError('Authentication expired. Please sign in again.');
      } else {
        _setError('Failed to load followed posts: $e');
      }
    }
  }

  /// Reset to top of feed immediately (for instant scroll response)
  void resetToTop() {
    AppLogger.provider(
      'resetToTop: Immediately scrolling to top of followed posts',
    );
    _currentPostIndex = 0;
    notifyListeners();
  }

  /// Refresh followed posts (pull to refresh) - optimized for better UX
  Future<void> refreshPosts() async {
    if (_status == FollowedPostsStatus.refreshing) return;

    // Check if user is authenticated before refreshing posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'refreshFollowedPosts: User not authenticated, skipping refresh',
      );
      _setError('User not authenticated. Please sign in to view posts.');
      return;
    }

    developer.log('FollowedPostsProvider: Starting to refresh followed posts');
    AppLogger.provider(
      'REFRESH TRIGGERED - Background refresh for latest followed content',
    );

    // Don't change status to refreshing immediately - keep current posts visible
    // Only set refreshing if we have no posts to show
    if (_posts.isEmpty) {
      _setStatus(FollowedPostsStatus.refreshing);
    }

    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: 0,
      );

      developer.log(
        'FollowedPostsProvider: Refreshed with ${posts.length} followed posts',
      );
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on refresh

      // Update cache with refreshed posts
      final feedState = FeedState(
        feedType: _feedType,
        posts: _posts,
        currentPostIndex: _currentPostIndex,
        lastRefreshTime: DateTime.now(),
        lastAccessTime: DateTime.now(),
        currentOffset: _currentOffset,
        hasMore: _hasMore,
      );
      _cacheManager.cacheFeed(feedState);

      _setStatus(FollowedPostsStatus.loaded);

      AppLogger.provider(
        'FOLLOWED REFRESH COMPLETED - ${posts.length} posts loaded',
      );
    } catch (e, stackTrace) {
      developer.log(
        'FollowedPostsProvider: Error refreshing followed posts',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'FollowedPostsProvider REFRESH ERROR',
        error: e,
        stackTrace: stackTrace,
      );

      // Only show error if we have no posts to fall back to
      if (_posts.isEmpty) {
        _setError('Failed to refresh followed posts: $e');
      } else {
        // Keep existing posts and just log the error
        AppLogger.provider(
          'Followed refresh failed but keeping existing posts',
        );
      }
    }
  }

  /// Load more followed posts (pagination)
  Future<void> loadMorePosts() async {
    if (!_hasMore || _status == FollowedPostsStatus.loading) return;

    // Check if user is authenticated before loading more posts
    if (!AwsAuthService.instance.isAuthenticated) {
      AppLogger.provider(
        'loadMoreFollowedPosts: User not authenticated, skipping load',
      );
      return;
    }

    try {
      final morePosts = await AwsPostsService.instance.getFollowedPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _posts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMore = morePosts.length == _pageSize;

        // Update cache with new posts
        _cacheManager.updateFeedPosts(_feedType, _posts);

        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more followed posts: $e');
    }
  }

  /// Set current post index for state persistence
  void setCurrentPostIndex(int index) {
    if (index >= 0 && index < _posts.length) {
      _currentPostIndex = index;
      // Update cache with new position
      _cacheManager.updateFeedPosition(_feedType, index);
      // Don't notify listeners for index changes to avoid rebuilds
    }
  }

  /// Mark that current posts are being displayed from cache
  void markAsDisplayingFromCache() {
    _currentlyDisplayingFromCache = true;
    notifyListeners();
  }

  /// Clear all posts and reset state
  void clearPosts() {
    _posts.clear();
    _currentOffset = 0;
    _hasMore = true;
    _currentPostIndex = 0;
    _setStatus(FollowedPostsStatus.initial);
    _error = null;
    notifyListeners();
  }

  /// Add a new post to the beginning of the list
  void addPost(PostModel post) {
    _posts.insert(0, post);
    _currentOffset++;
    _cacheManager.updateFeedPosts(_feedType, _posts);
    notifyListeners();
  }

  /// Update an existing post
  void updatePost(PostModel updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      _cacheManager.updateFeedPosts(_feedType, _posts);
      notifyListeners();
    }
  }

  /// Remove a post
  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    _currentOffset = _posts.length;
    _cacheManager.updateFeedPosts(_feedType, _posts);
    notifyListeners();
  }

  /// Private helper methods
  void _setStatus(FollowedPostsStatus status) {
    _status = status;
    if (status != FollowedPostsStatus.error) {
      _error = null;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _status = FollowedPostsStatus.error;
    notifyListeners();
  }
}
