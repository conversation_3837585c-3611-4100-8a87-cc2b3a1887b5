import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/aws_auth_service.dart' as aws;
import '../services/apple_signin_service.dart';
import '../services/xbox_auth_service.dart';
import '../services/twitch_auth_service.dart';
import '../services/kick_auth_service.dart';
import '../services/amplitude_service.dart';
import '../services/notification_service.dart';
import '../services/background_profile_cache_service.dart';
import '../utils/app_logger.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  usernameRequired,
  emailVerificationRequired,
  error,
}

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  String? _pendingVerificationEmail;
  StreamSubscription<aws.AuthState>? _authSubscription;
  Map<String, dynamic>? _accountChoiceData;

  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  String? get pendingVerificationEmail => _pendingVerificationEmail;
  Map<String, dynamic>? get accountChoiceData => _accountChoiceData;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get requiresUsername => _status == AuthStatus.usernameRequired;
  bool get requiresEmailVerification =>
      _status == AuthStatus.emailVerificationRequired;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // Initialize background profile cache service
      await BackgroundProfileCacheService.instance.initialize();

      // Try to restore session from stored tokens
      final sessionRestored =
          await aws.AwsAuthService.instance.restoreSession();

      if (sessionRestored && aws.AwsAuthService.instance.isAuthenticated) {
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          _setStatus(AuthStatus.authenticated);

          // Set user ID for Amplitude session tracking on session restore
          if (_user?.id != null) {
            AmplitudeService.instance.setUserId(_user!.id);
            AppLogger.auth(
              'Amplitude User ID set on session restore: ${_user!.id}',
            );

            // Preload user profile in background
            BackgroundProfileCacheService.instance.onUserAuthenticated(
              _user!.id,
            );
          }
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }

      // Listen to auth state changes
      _authSubscription = aws.AwsAuthService.instance.authStateChanges.listen(
        _onAuthStateChange,
        onError: (error) {
          _setError('Authentication error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  /// Handle auth state changes
  void _onAuthStateChange(aws.AuthState authState) {
    switch (authState) {
      case aws.AuthState.signedIn:
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          // For session restoration, always set to authenticated
          // Username requirements are only checked during initial authentication flows
          _setStatus(AuthStatus.authenticated);

          // Set user ID for Amplitude session tracking on auth state change
          if (_user?.id != null) {
            AmplitudeService.instance.setUserId(_user!.id);
            AppLogger.auth(
              'Amplitude User ID set on auth state change: ${_user!.id}',
            );

            // Preload user profile in background
            BackgroundProfileCacheService.instance.onUserAuthenticated(
              _user!.id,
            );
          }

          // Register device token for notifications
          _registerDeviceTokenAsync();
        }
        break;
      case aws.AuthState.signedOut:
        _user = null;
        _setStatus(AuthStatus.unauthenticated);

        // Clear user ID from Amplitude on sign out
        AmplitudeService.instance.clearUserId();
        AmplitudeService.instance.trackEvent('user_signout', {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });

        // Notify background profile cache service of sign out
        BackgroundProfileCacheService.instance.onUserSignedOut();
        break;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign up process for email: $email');
    AppLogger.auth('Starting sign up process for email: $email');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.signUp(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign up response received - Success: ${response.success}',
      );
      AppLogger.auth('Sign up response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success) {
        developer.log('AuthProvider: Sign up completed successfully');
        AppLogger.auth('Sign up completed successfully');

        // Set email verification required status
        _pendingVerificationEmail = email;
        _setStatus(AuthStatus.emailVerificationRequired);

        developer.log('AuthProvider: Email verification required for: $email');
        AppLogger.auth('Email verification required for: $email');

        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Sign up error: $errorMsg');
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign up failed: $e';
      developer.log(
        'AuthProvider: Error during sign up',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SIGN UP ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign in process for email: $email');
    AppLogger.auth('Starting sign in process for email: $email');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.signIn(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign in response received - Success: ${response.success}',
      );
      AppLogger.auth('Sign in response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success && response.user != null) {
        _user = UserModel.fromAwsUser(response.user!);

        // Check if username is required
        if (response.requiresUsername == true) {
          _setStatus(AuthStatus.usernameRequired);
          developer.log(
            'AuthProvider: Sign in successful but username required',
          );
          AppLogger.auth('Sign in successful but username required');
        } else {
          _setStatus(AuthStatus.authenticated);
          developer.log('AuthProvider: Sign in completed successfully');
          AppLogger.auth('Sign in completed successfully');

          // Track successful login in Amplitude
          AmplitudeService.instance.trackEvent('user_authentication', {
            'method': 'email_password',
            'success': true,
            'user_id': _user?.id ?? 'unknown',
          });

          // Set user ID for Amplitude session tracking
          if (_user?.id != null) {
            AmplitudeService.instance.setUserId(_user!.id);

            // Preload user profile in background
            BackgroundProfileCacheService.instance.onUserAuthenticated(
              _user!.id,
            );
          }
        }
        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Sign in error: $errorMsg');

        // Check if error is due to unverified email
        if (_isEmailVerificationError(errorMsg)) {
          _pendingVerificationEmail = email;
          _setStatus(AuthStatus.emailVerificationRequired);
          developer.log(
            'AuthProvider: Email verification required for sign in',
          );
          AppLogger.auth('Email verification required for sign in');
        } else {
          _setErrorWithoutStatusChange(errorMsg);
        }
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign in failed: $e';
      developer.log(
        'AuthProvider: Error during sign in',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setStatus(AuthStatus.loading);

    try {
      await aws.AwsAuthService.instance.signOut();
      _user = null;
      _setStatus(AuthStatus.unauthenticated);

      // Clear user ID from Amplitude on manual sign out
      AmplitudeService.instance.clearUserId();
      AmplitudeService.instance.trackEvent('user_signout', {
        'method': 'manual',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      _setError('Sign out failed: $e');
    }
  }

  /// Sign in with Apple
  Future<bool> signInWithApple() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Apple Sign In');
      AppLogger.auth('Starting Apple Sign In');

      final response = await AppleSignInService.instance.signIn();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Store the tokens and user data in AWS auth service
        await aws.AwsAuthService.instance.storeAppleAuthData(
          response.user!,
          response.tokens!.accessToken,
          response.tokens!.refreshToken,
          response.tokens!.idToken,
        );

        _user = response.user;

        // For third-party auth (Apple), skip username requirement
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Apple Sign In successful');
        AppLogger.auth('Apple Sign In successful');

        // Track successful Apple login in Amplitude
        AmplitudeService.instance.trackEvent('user_authentication', {
          'method': 'apple_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for Amplitude session tracking
        if (_user?.id != null) {
          AmplitudeService.instance.setUserId(_user!.id);

          // Preload user profile in background
          BackgroundProfileCacheService.instance.onUserAuthenticated(_user!.id);
        }
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Apple Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Apple Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: APPLE SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with Xbox
  Future<bool> signInWithXbox() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Xbox Sign In');
      AppLogger.auth('Starting Xbox Sign In');

      final response = await XboxAuthService.instance.signInWithXbox();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Store the tokens and user data in AWS auth service
        await aws.AwsAuthService.instance.storeXboxAuthData(
          response.user!,
          response.tokens!.accessToken,
          response.tokens!.refreshToken,
          response.tokens!.idToken,
        );

        _user = response.user;

        // For third-party auth (Xbox), skip username requirement
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Xbox Sign In successful');
        AppLogger.auth('Xbox Sign In successful');

        // Track successful Xbox login in Amplitude
        AmplitudeService.instance.trackEvent('user_authentication', {
          'method': 'xbox_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for Amplitude session tracking
        if (_user?.id != null) {
          AmplitudeService.instance.setUserId(_user!.id);

          // Preload user profile in background
          BackgroundProfileCacheService.instance.onUserAuthenticated(_user!.id);
        }
        return true;
      } else if (response.requiresAccountChoice) {
        // Account choice required - navigate to account choice screen
        _setStatus(AuthStatus.unauthenticated);

        // Store the account choice data for navigation
        _accountChoiceData = {
          'accountOptions': response.accountOptions,
          'xboxData': response.xboxData,
        };

        developer.log('AuthProvider: Xbox account choice required');
        AppLogger.auth('Xbox account choice required');

        return true; // Return true to indicate we should navigate to account choice
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Xbox Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Xbox Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: XBOX SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with Twitch
  Future<bool> signInWithTwitch() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Twitch Sign In');
      AppLogger.auth('Starting Twitch Sign In');

      final response = await TwitchAuthService.instance.signInWithTwitch();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Convert user data to UserModel and store tokens
        final userModel = UserModel.fromJson(response.user!);

        await aws.AwsAuthService.instance.storeTwitchAuthData(
          userModel,
          response.tokens!['accessToken'],
          response.tokens!['refreshToken'],
          response.tokens!['idToken'],
        );

        _user = userModel;

        // For third-party auth (Twitch), skip username requirement
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Twitch Sign In successful');
        AppLogger.auth('Twitch Sign In successful');

        // Track successful Twitch login in Amplitude
        AmplitudeService.instance.trackEvent('user_authentication', {
          'method': 'twitch_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for Amplitude session tracking
        if (_user?.id != null) {
          AmplitudeService.instance.setUserId(_user!.id);

          // Preload user profile in background
          BackgroundProfileCacheService.instance.onUserAuthenticated(_user!.id);
        }
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Twitch Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Twitch Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: TWITCH SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with Kick
  Future<bool> signInWithKick() async {
    _setStatus(AuthStatus.loading);

    try {
      developer.log('AuthProvider: Starting Kick Sign In');
      AppLogger.auth('Starting Kick Sign In');

      final response = await KickAuthService.instance.signInWithKick();

      if (response.success &&
          response.user != null &&
          response.tokens != null) {
        // Convert user data to UserModel and store tokens
        final userModel = UserModel.fromJson(response.user!);

        await aws.AwsAuthService.instance.storeKickAuthData(
          userModel,
          response.tokens!['accessToken'],
          response.tokens!['refreshToken'],
          response.tokens!['idToken'],
        );

        _user = userModel;

        // For third-party auth (Kick), skip username requirement
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Kick Sign In successful');
        AppLogger.auth('Kick Sign In successful');

        // Track successful Kick login in Amplitude
        AmplitudeService.instance.trackEvent('user_authentication', {
          'method': 'kick_signin',
          'success': true,
          'user_id': _user?.id ?? 'unknown',
        });

        // Set user ID for Amplitude session tracking
        if (_user?.id != null) {
          AmplitudeService.instance.setUserId(_user!.id);

          // Preload user profile in background
          BackgroundProfileCacheService.instance.onUserAuthenticated(_user!.id);
        }
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Kick Sign In failed: $e';
      developer.log(
        'AuthProvider: Error during Kick Sign In',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: KICK SIGN IN ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Initiate forgot password flow
  Future<aws.AwsAuthResponse> forgotPassword(String email) async {
    developer.log('AuthProvider: Starting forgot password for email: $email');
    AppLogger.auth('Starting forgot password for email: $email');

    try {
      final response = await aws.AwsAuthService.instance.forgotPassword(email);

      developer.log(
        'AuthProvider: Forgot password response - Success: ${response.success}',
      );
      AppLogger.auth('Forgot password response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      return response;
    } catch (e, stackTrace) {
      final errorMsg = 'Forgot password failed: $e';
      developer.log(
        'AuthProvider: Error during forgot password',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: FORGOT PASSWORD ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      return aws.AwsAuthResponse(success: false, message: errorMsg);
    }
  }

  /// Confirm forgot password with verification code and new password
  Future<aws.AwsAuthResponse> confirmForgotPassword(
    String email,
    String confirmationCode,
    String newPassword,
  ) async {
    developer.log('AuthProvider: Confirming forgot password for email: $email');
    AppLogger.auth('Confirming forgot password for email: $email');

    try {
      final response = await aws.AwsAuthService.instance.confirmForgotPassword(
        email: email,
        confirmationCode: confirmationCode,
        newPassword: newPassword,
      );

      developer.log(
        'AuthProvider: Confirm forgot password response - Success: ${response.success}',
      );
      AppLogger.auth(
        'Confirm forgot password response - Success: ${response.success}',
      );
      AppLogger.auth('Message: ${response.message}');

      return response;
    } catch (e, stackTrace) {
      final errorMsg = 'Confirm forgot password failed: $e';
      developer.log(
        'AuthProvider: Error during confirm forgot password',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: CONFIRM FORGOT PASSWORD ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      return aws.AwsAuthResponse(success: false, message: errorMsg);
    }
  }

  /// Change password for the current user (not implemented in AWS backend yet)
  Future<bool> changePassword(String newPassword) async {
    _setError('Password change is not implemented yet');
    return false;
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  /// Handle successful email verification
  void onEmailVerified() {
    developer.log('AuthProvider: Email verification completed');
    AppLogger.auth('Email verification completed');

    _pendingVerificationEmail = null;
    _setStatus(AuthStatus.unauthenticated);
  }

  /// Clear pending verification and return to unauthenticated state
  void clearPendingVerification() {
    developer.log('AuthProvider: Clearing pending verification');
    AppLogger.auth('Clearing pending verification');

    _pendingVerificationEmail = null;
    _setStatus(AuthStatus.unauthenticated);
  }

  /// Check if error message indicates email verification is required
  bool _isEmailVerificationError(String? errorMessage) {
    if (errorMessage == null) return false;
    final lowerError = errorMessage.toLowerCase();
    return lowerError.contains('user not confirmed') ||
        lowerError.contains('account not confirmed') ||
        lowerError.contains('please verify your email') ||
        lowerError.contains('email not verified');
  }

  /// Set error without changing status (for login/signup failures)
  /// Set user from Xbox account creation
  void setUserFromXboxCreation(UserModel user) {
    _user = user;
    _setStatus(AuthStatus.usernameRequired);
    developer.log('AuthProvider: User set from Xbox account creation');
    AppLogger.auth('User set from Xbox account creation');
  }

  /// Set username for authenticated user
  Future<bool> setUsername({required String username}) async {
    developer.log('AuthProvider: Setting username: $username');
    AppLogger.auth('Setting username: $username');

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.setUsername(
        username: username,
      );

      developer.log(
        'AuthProvider: Set username response received - Success: ${response.success}',
      );
      AppLogger.auth('Set username response - Success: ${response.success}');
      AppLogger.auth('Message: ${response.message}');

      if (response.success && response.user != null) {
        _user = UserModel.fromAwsUser(response.user!);
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Username set successfully');
        AppLogger.auth('Username set successfully');

        // Set user ID for Amplitude session tracking after username completion
        if (_user?.id != null) {
          AmplitudeService.instance.setUserId(_user!.id);
          AppLogger.auth(
            'Amplitude User ID set after username completion: ${_user!.id}',
          );

          // Preload user profile in background
          BackgroundProfileCacheService.instance.onUserAuthenticated(_user!.id);
        }

        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        AppLogger.auth('Set username error: $errorMsg');
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Set username failed: $e';
      developer.log(
        'AuthProvider: Error during set username',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AuthProvider: SET USERNAME ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  void _setErrorWithoutStatusChange(String error) {
    _errorMessage = error;
    // Reset loading status back to unauthenticated if we're currently loading
    if (_status == AuthStatus.loading) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  /// Update the current user model
  void updateUser(UserModel updatedUser) {
    _user = updatedUser;
    notifyListeners();
  }

  /// Register device token for notifications (async)
  void _registerDeviceTokenAsync() {
    // Run device token registration in background without blocking UI
    Future.microtask(() async {
      try {
        await NotificationService.instance.registerDeviceToken();
        AppLogger.auth('Device token registered successfully');
      } catch (e) {
        AppLogger.error('Failed to register device token: $e');
        // Don't throw error as this shouldn't block authentication
      }
    });
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
