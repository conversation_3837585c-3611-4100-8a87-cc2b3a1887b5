import 'dart:async';
import 'dart:developer' as developer;
import '../services/aws_auth_service.dart';
import '../providers/user_profile_provider.dart';
import '../utils/app_logger.dart';

/// Service responsible for background profile caching and preloading
/// Ensures user profile data is always available from cache without waiting
class BackgroundProfileCacheService {
  static final BackgroundProfileCacheService _instance =
      BackgroundProfileCacheService._internal();
  static BackgroundProfileCacheService get instance => _instance;
  BackgroundProfileCacheService._internal();

  // State tracking
  bool _isInitialized = false;
  bool _isPreloading = false;
  String? _currentUserId;
  Timer? _refreshTimer;

  // Configuration
  static const Duration _preloadDelay = Duration(milliseconds: 100);
  static const Duration _backgroundRefreshInterval = Duration(minutes: 30);
  static const Duration _foregroundRefreshDelay = Duration(milliseconds: 500);

  /// Initialize the background profile cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('BackgroundProfileCacheService: Initializing');
      _isInitialized = true;

      // Start background refresh timer
      _startBackgroundRefreshTimer();

      AppLogger.info('BackgroundProfileCacheService: Initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'BackgroundProfileCacheService: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Preload current user's profile in background immediately after authentication
  Future<void> preloadCurrentUserProfile() async {
    if (_isPreloading) {
      AppLogger.debug(
        'BackgroundProfileCacheService: Preload already in progress',
      );
      return;
    }

    try {
      final currentUser = AwsAuthService.instance.currentUser;
      if (currentUser == null) {
        AppLogger.debug(
          'BackgroundProfileCacheService: No authenticated user for preload',
        );
        return;
      }

      _currentUserId = currentUser.id;
      _isPreloading = true;

      AppLogger.info(
        'BackgroundProfileCacheService: Starting profile preload for user: ${currentUser.id}',
      );

      // Add small delay to ensure UI is ready and not blocking main thread
      await Future.delayed(_preloadDelay);

      // Check if we already have cached data (cache never expires until logout)
      final cachedProfile = UserProfileProvider.instance.getCachedProfile(
        currentUser.id,
      );
      if (cachedProfile != null) {
        AppLogger.info(
          'BackgroundProfileCacheService: Profile already cached for user: ${currentUser.id}',
        );
        _isPreloading = false;
        return;
      }

      // Load profile in background without affecting UI
      await _loadProfileInBackground(currentUser.id);

      AppLogger.info(
        'BackgroundProfileCacheService: Profile preload completed for user: ${currentUser.id}',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'BackgroundProfileCacheService: Error during profile preload',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _isPreloading = false;
    }
  }

  /// Refresh current user's profile in background (called on app foreground)
  Future<void> refreshCurrentUserProfile() async {
    if (_currentUserId == null) {
      AppLogger.debug(
        'BackgroundProfileCacheService: No current user for refresh',
      );
      return;
    }

    try {
      AppLogger.info(
        'BackgroundProfileCacheService: Refreshing profile for user: $_currentUserId',
      );

      // Add delay to ensure app is fully resumed
      await Future.delayed(_foregroundRefreshDelay);

      // Refresh profile in background
      await _loadProfileInBackground(_currentUserId!, forceRefresh: true);

      AppLogger.info(
        'BackgroundProfileCacheService: Profile refresh completed for user: $_currentUserId',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'BackgroundProfileCacheService: Error during profile refresh',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Load profile data in background without affecting UI state
  Future<void> _loadProfileInBackground(
    String userId, {
    bool forceRefresh = false,
  }) async {
    try {
      // Use UserProfileProvider to load profile with caching
      // This will update the cache but won't trigger UI updates if no one is listening
      await UserProfileProvider.instance.loadUserProfile(
        userId,
        loadPosts: false, // Don't load posts in background to save bandwidth
        forceRefresh: forceRefresh,
      );

      developer.log(
        'BackgroundProfileCacheService: Successfully loaded profile in background for user: $userId',
      );
    } catch (e) {
      developer.log(
        'BackgroundProfileCacheService: Error loading profile in background: $e',
      );
      // Don't rethrow - background loading should be silent
    }
  }

  /// Start background refresh timer for periodic cache updates
  void _startBackgroundRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(_backgroundRefreshInterval, (timer) {
      if (_currentUserId != null) {
        _loadProfileInBackground(_currentUserId!, forceRefresh: true);
      }
    });
  }

  /// Handle user authentication change
  void onUserAuthenticated(String userId) {
    _currentUserId = userId;

    // Preload profile immediately
    Future.microtask(() => preloadCurrentUserProfile());
  }

  /// Handle user sign out
  void onUserSignedOut() {
    _currentUserId = null;
    _refreshTimer?.cancel();

    // Clear all cached profile data on logout
    UserProfileProvider.clearAllCache();

    AppLogger.info(
      'BackgroundProfileCacheService: User signed out, cleared cache and stopped background refresh',
    );
  }

  /// Handle app coming to foreground
  void onAppForeground() {
    if (_currentUserId != null) {
      // Refresh profile when app comes back to foreground
      Future.microtask(() => refreshCurrentUserProfile());
    }
  }

  /// Handle app going to background
  void onAppBackground() {
    // Stop background refresh timer to save battery
    _refreshTimer?.cancel();
    AppLogger.debug(
      'BackgroundProfileCacheService: App backgrounded, paused refresh timer',
    );
  }

  /// Handle app resumed (from inactive state)
  void onAppResumed() {
    // Restart background refresh timer
    _startBackgroundRefreshTimer();

    if (_currentUserId != null) {
      // Refresh profile when app resumes
      Future.microtask(() => refreshCurrentUserProfile());
    }

    AppLogger.debug(
      'BackgroundProfileCacheService: App resumed, restarted refresh timer',
    );
  }

  /// Get current user ID being cached
  String? get currentUserId => _currentUserId;

  /// Check if service is currently preloading
  bool get isPreloading => _isPreloading;

  /// Dispose of the service
  void dispose() {
    _refreshTimer?.cancel();
    _isInitialized = false;
    _currentUserId = null;
    AppLogger.info('BackgroundProfileCacheService: Disposed');
  }
}
